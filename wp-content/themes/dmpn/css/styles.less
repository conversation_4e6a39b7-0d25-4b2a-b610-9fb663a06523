// out: styles.min.css, compress: true

@font: 'Open Sans', sans-serif;

@extra: 800;
@bold: 700;
@reg: 400;
@light: 300;

@dark: #333333;
@blue: #299BDE;
@gold: #B18623;


html {
    font-size: 62.5%;
}

body {
    font-family: @font;
    font-weight: @reg;
    font-size: 2rem;
    line-height: 27px;
    color: @dark;

    overflow-x: hidden;
}

h1,h2,h3,h4,h5,h6 {
    margin-top: 0 !important;
    margin-bottom: 50px
}

h1 {
    font-size: 9rem;
    line-height: 97px;
    font-weight: @extra;
}

h2 {
    font-size: 4.5rem;  
    line-height: 61px;
    font-weight: @extra;
}

h3 {
    font-size: 2.4rem;
    line-height: 32px;
    font-weight: @reg;
    margin-bottom: 25px;
}

h4 {
    font-size: 2.8rem;
    line-height: 35px;
    font-weight: @extra;
}

p {
    font-size: 2.4rem;
    line-height: 40px;
}

em {
    color: inherit !important;
}

.uk-justify-sb {
    justify-content: space-between;
}

.wp-block-group {
    .wp-block-group__inner-container {
        display: flow-root;
        box-sizing: content-box;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
        padding-left: 15px;
        padding-right: 15px;

        @media (min-width: 960px) {
            padding-left: 40px;
            padding-right: 40px;
        }
    }
    &.newsletter-group {
        form {
            display: flex;
            align-items: center;

            .gform_body {
                flex-grow: 1;
                margin-right: 15px;

                input {
                    padding: 1em 1.5em !important;
                }
            }

            .gform_footer {
                margin: 0;
                padding: 0;

                input {
                    margin: 0;
                    padding: 0;
                }
            }
        }
    }
}

.wp-block-separator {
    border-top: 5px solid black;
    border-bottom: 0;
}

.wp-block-column:not(:first-child) {
    margin-left: 4em;
}

.wp-block-image {
    figure {
        &.aligncenter {
            figcaption {
                text-align: center;
            }
        }

        figcaption {
            font-size: 2.4rem;
            margin-top: 0;
        }
    }
}

.wp-block-buttons {
    &.is-style-flex {
        justify-content: space-between;
    }

    .wp-block-button {

        .wp-block-button__link {
            background: transparent;
            border-radius: 50px;
            border: 2px solid @blue;
            color: @blue;
            font-size: 2rem;
            font-weight: @bold;
            padding: 12px 35px;
            transition: all 250ms;

            &:hover {
                background: @blue;
                text-decoration: none;
                color: white;
            }
        }

        &.is-style-square {
            background: #313131;
            position: relative;

            .wp-block-button__link {
                display: block;
                font-size: 3.2rem;
                border-radius: 0;
                text-align: left;
                line-height: 40px;
                background: transparent;
                padding: 29px 125px 29px 40px;
                cursor: pointer;
                color: white;
                border: none;
                font-weight: 100;

                strong {
                    font-style: italic;
                    font-weight: @extra;
                }

                &:hover {
                    text-decoration: none;
                }
            }

            &::after {
                content: "";
                position: absolute;
                right: 40px;
                top: 50%;
                transform: translateY(-50%);
                background: url(../img/gold-arrow.svg) no-repeat;
                width: 28px;
                height: 50px;
                margin-left: 35px;
                transition: all 0.25s;
            }

            &:hover {
                &::after {
                    right: 25px;
                }
            }
        }
    }
}

a {
    &.button {
        border-radius: 50px;
        padding: 8px 25px;
        font-weight: @bold;
        transition: background 0.35s;
        font-size: 1.6rem;

        &:hover {
            text-decoration: none;
        }
    }

    &.blue {
        border-radius: 50px;
        border: 3px solid @blue;
        color: @blue;
        padding: 13px 25px;
        font-weight: @bold;
        transition: background 0.35s;

        span {
            position: relative;
            top: 5px;
        }

        &:hover {
            color: white;
            background: @blue;
            text-decoration: none;

            span {
                background: url(../img/white-wave.svg);
            }
        }
    }
}

span {
    &.icon-podcast-blue {
        background: url(../img/blue-wave.svg);
        display: inline-block;
        width: 38px;
        height: 26px;
    }
}

form {
    &.search {
        position: relative;
        max-width: 320px;
        float: right;

        .search-input {
            padding: .8em 1.5em;
            border: 2px solid gray;
            border-radius: 50px;
        }

        .search-submit {
            background: transparent;
            border: none;
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
        }
    }

    input, select, textarea {
        background: #F2F2F2;
        padding: 1em !important;
        font-size: 2rem !important;
        border-radius: 0;
        border: 1px solid #F2F2F2;
    }

    select {
        -webkit-appearance: none;
        appearance: none;
        padding-right: 50px !important;
    }

    .ginput_container_select {
        position: relative;

        &::after {
            content: "";
            background: url(../img/select-dropdown.svg) no-repeat;
            width: 17px;
            height: 9px;
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    input[type=submit] {
        background: transparent;
        border-radius: 50px;
        border: 3px solid @blue;
        color: @blue;
        padding: 13px 25px !important;
        font-weight: @bold;
        transition: all 0.35s;
        min-width: 176px;
        margin-top: 25px;

        &:hover {
            color: white;
            background: @blue;
        }
    }
}

div {
    &.listens {
        position: relative;
        padding: 1em 0;
        background-color: #313131;

        p {
            color: #717171;
            font-size: 3.2rem;
            line-height: 40px;
            margin: 0;

            span {
                color: white;
                font-weight: @extra;
            }
        }

        .powered-container {
            position: absolute;
            background: #4a4a4a;
            padding-left: 50px;
            padding-right: 30px;
            right: 0;
            top: 0;
            z-index: 10;

            a {
                display: flex;
                height: 80px;
            }

            &::before {
                content: "";
                width: 0;
                height: 0;
                border-top: 40px solid transparent;
                border-bottom: 40px solid transparent;
                border-left: 30px solid #313131;
                position: absolute;
                top: 0;
                left: 0;

            }
        }
    }

    &.single-post {
        .intro {
            margin-top: 60px;

            .author {
                .avatar-container {
                    background-color: white;
                    padding: 4px;
                    border-radius: 100%;
                    margin-right: 25px;

                    .avatar {
                        border-radius: 100%;
                        width: 90px;
                        height: 90px;
                    }
                }

                p {
                    font-style: italic;
                }
            }
        }

        .post-body {
            padding-top: 150px;

            .content {
                padding-bottom: 80px;
                margin-bottom: 80px;
                border-bottom: 4px solid #C4C4C4;
            }
        }

        .post-share {
            p {
                margin: 0 25px 0 0;
            }

            a {
                margin: 0 6px;
            }

            span {
                width: 39px;
                height: 39px;
                display: inline-block;

                &.facebook {
                    background: url(../img/facebook.svg) no-repeat;
                }
                
                &.twitter {
                    background: url(../img/twitter.svg) no-repeat;
                }
                
                &.linkedin {
                    background: url(../img/linkedin.svg) no-repeat;
                }

                &.pinterest {
                    background: url(../img/pinterest.svg) no-repeat;
                }
            }
        }
    }
}

header.header {
    padding: 55px 0;
    position: relative;
    z-index: 999;

    .powered {
        position: absolute;
        right: 40px;
        top: -10px;
    }

    .uk-container {
        position: relative;
    }

    nav {
        margin-left: auto;

        .menu {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
            margin-right:  -1em;

            li {
                padding: 1em 2em;

                a {
                    display: block;
                    font-weight: @bold;
                    font-size: 2rem;
                    color: @dark;
                    position: relative;
                    padding-bottom: 10px;
                    transition: all 250ms;

                    &:hover {
                        text-decoration: none;
                        color: @gold;

                        &::after {
                            transform: translateX(-50%) scale(1);
                        }
                    }

                    &::after {
                        content: "";
                        position: absolute;
                        left: 50%;
                        bottom: 0;
                        transform: translateX(-50%) scaleX(0);
                        transform-origin: 50% 50%;
                        width: 100%;
                        height: 4px;
                        background-color: #DBDBDB;
                        transition: transform 250ms;
                    }
                }

                &.menu-item-has-children {
                    position: relative;

                    &:hover {
                        .sub-menu {
                            display: block;
                            left: 0;
                        }
                    }
                }
            }
        }

        .sub-menu {
            display: none;
            position: absolute;
            left: -999em;
            top: 75px;
            background: white;
            padding: 1.5em;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            z-index: 999;
            margin: 0;
            list-style: none;
            padding: 0;
            min-width: 320px;
            border-radius: 6px;

            &::before {
                content: "";
                background: url(../img/dropdown.svg) no-repeat;
                width: 38px;
                height: 23px;
                position: absolute;
                top: -15px;
                left: 62px;
            }

            li {
                padding: 0;
                a {
                    padding: 1.8em 2em;
                    border-bottom: 1px solid #E6E6E6;
                    border-left: 7px solid transparent;

                    &::after {
                        display: none;
                    }

                    &:hover {
                        border-left: 7px solid @gold;
                    }
                }

                &:last-child {
                    a {
                        border-bottom: 0;
                    }
                }
            }
        }
    }
}

main {
    &.has-pattern {
        position: relative;

        &::after {
            content:"";
            background: url(../img/page-pattern.jpg) no-repeat center bottom / cover;
            width: 100%;
            height: 810px;
            opacity: 70%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
        }
    }
}

section {
    &.r-banner {
        .banner__inner {
            position: relative;
            padding: 90px 240px 90px 80px;
            min-height: 508px;

            &::before {
                content: "";
                background: rgba(0,0,0,0.50);
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
            }

            * {
                position: relative;
                color: white;
                z-index: 2;
            }

            h3 {
                letter-spacing: 8px;
            }

            img.podcast {
                position: absolute;
                bottom: 30px;
                right: 30px;
            }
        }
    }

    &.r-contact {
        h2 {
            margin-bottom: 55px;
        }

        p {
            max-width: 320px;
        }

        i {
            font-size: 2.5rem;
            margin-left: 15px;
            position: relative;
            top: 3px;
        }
    }

    &.r-cta {
        background: #313131;

        .image-container {
            position: relative;
            .image {
                position: relative;
                width: 400px;
            }

            img {
                position: absolute;
                right: 25px;
                bottom: 25px;
                z-index: 5;
            }
        }


        .text {
            padding: 0 3em;

            p {
                margin: 0;
                font-size: 2.4rem;
                line-height: 40px;
                color: white;
            }
        }
    }

    &.r-podcasts {
        padding: 100px 0 0;

        .podcast {
            position: relative;
            padding: 85px 0;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                right: 0;
                width: 73%;
                height: 5px;
                background: @dark;
            }

            h3 {
                font-size: 2.8rem;
                line-height: 35px;
                font-weight: @extra;
            }

            p {
                font-style: italic;
                font-size: 2.4rem;
                line-height: 32px;
                font-weight: 300;
                margin: 30px 0 50px;
            }

            span.number {
                font-size: 30rem;
                color: white;
                text-shadow: 0 0px 2px black;
                font-weight: 700;
                line-height: 300px;
            }

        }
    }

    &.r-posts {

        .r-most-recent {
            padding: 80px 0 110px;
            border-bottom: 5px solid black;
            margin-bottom: 80px;

            .post {
                margin-bottom: 0;
            }
        }

        .post {
            margin: 0 0 100px 0;

            .bg {
                min-height: 367px;
                box-shadow:0 10px 25px rgba(#185D86, 25%);
                margin-bottom: 30px;
            }

            h3 {
                font-size: 2.8rem;
                line-height: 35px;
                color: @dark;
                font-weight: @extra;

                a {
                    color: inherit;

                    &:hover {
                        text-decoration: none;
                    }
                }
            }

            p {
                font-size: 2.4rem;
                line-height: 32px;
                font-style: italic;
                font-weight: 300;
                margin-bottom: 45px;
            }
        }

        .pagination {
            text-align: center;
            margin-top: 80px;

            .page-numbers {
                color: @dark;

                &.current {
                    color: @blue;
                    font-weight: @extra;
                }
            }
        }
    }

    &.r-tabs {
        .uk-accordion {
            margin: 0;

            li {
                position: relative;

                &.uk-open {
                    .uk-accordion-title {
                        background: @gold;

                        &::after {
                            background: url(../img/white-x.svg);
                            width: 50px;
                        }
                    }
                }

                .uk-accordion-title {
                    background: #313131;
                }

                a {
                    display: block;
                    font-size: 3.2rem;
                    border-radius: 0;
                    text-align: left;
                    line-height: 40px;
                    background: transparent;
                    padding: 29px 125px 29px 40px;
                    cursor: pointer;
                    color: white;
                    text-transform: none;

                    &::before {
                        display: none;
                    }

                    &::after {
                        content: "";
                        position: absolute;
                        right: 40px;
                        top: 45px;
                        background: url(../img/gold-arrow.svg) no-repeat;
                        width: 28px;
                        height: 50px;
                        margin-left: 35px;
                        transition: all .25s;
                        transform-origin: center center;
                    }
                }

            }
        }

        .uk-switcher {
            margin-top: 50px !important;

            li {
                padding: 5%;
            }
        }
    }

    &.r-single-podcast {
        margin-top: 105px;

        p {
            font-size: 2rem;
            line-height: 36px;
            margin-bottom: 45px;

            strong {
                margin-right: 40px;
            }
        }
    }

    &.r-episode {
        margin-bottom: 20px;

        .r-episode__inner {
            position: relative;
            background: #F7F7F7;
            padding: 30px 36px 30px 60px;

            h3 {
                margin: 0;
            }

            .button {
                background-color: white;
            }
        }

        span.border-accent {
            width: 9px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
        }
    }
}

footer.footer {
    padding: 11em 0 5em;

    p,a {
        margin: 0;
        font-size: 1rem;
        line-height: 27px;
    }

    a {
        color: @dark;
        margin-right: 10px;

        &::after {
            content: "";
            display: inline-block;
            width: 1px;
            height: 8px;
            position: relative;
            background: @dark;
            margin-left: 10px;
            top: 1px;
        }
    }

    .foot-logo {
        background: white;
        padding: 2em;
        position: absolute;
        top: -147px;
        left: 50%;
        transform: translateX(-50%);
    }

    .uk-container {
        position: relative;

        &::before {
            content: "";
            width: 95%;
            height: 5px;
            background: @dark;
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

@media (max-width: 1200px) {
    .wp-block-buttons {
        &.is-style-flex {
            display: block;
        }
        .wp-block-button {
            &.is-style-square {
                display: inline-block;
                width: 32%;
                .wp-block-button__link {
                    padding: 20px 20px 20px 25px;
                }
            }
        }
    }
}

@media (max-width: 960px) {

    .wp-block-group {
        .wp-block-group__inner-container {
            padding-left: 30px;
            padding-right: 30px;
        }
    }

    .wp-block-buttons {
        &.is-style-flex {
            display: block;
        }

        .wp-block-button {
            &.is-style-square {
                display: block;
                width: 100%;

                .wp-block-button__link {
                    padding: 15px 15px 15px 20px;
                }
            }
        }
    }

    .wp-block-spacer {
        &.responsive {
            height: 85px !important;
        }
    }

    .uk-offcanvas-bar {
        .menu {
            margin: 50px 0 0 0;
            padding: 0;
            list-style: none;

            li {
                a {
                    display: block;
                    padding: .5em;
                }

                &.current_page_item {
                    a {
                        color: @gold;
                    }
                }

                &.menu-item-has-children {
                    position: relative;

                    &::after {
                        content: "";
                        background: url(../img/nav-arrow.svg) no-repeat;
                        background-size: contain;
                        width: 20px;
                        height: 20px;
                        position: absolute;
                        right: 12px;
                        top: 19px;
                        transform: rotate(0);
                        transition: all 250ms;
                    }

                    .sub-menu {
                        padding: 0;
                        list-style: none;
                        background: #f5f5f5;
                        display: none;

                        li {
                            a {
                                color: @dark;
                            }
                        }
                    }
                }

                &.active {
                    .sub-menu {
                        display: block;
                    }

                    &::after {
                        transform: rotate(90deg);
                    }
                }
            }
        }
    }

    h1 {
        font-size: 7rem;
        line-height: 75px;
    }

    main {
        &.has-pattern {
            &::after {
                height: 620px;
            }
        }
    }

    header.header {
        nav {
            margin-left: 2em;

            .menu {
                li {
                    padding: 0 1.2em;
                }
            }
        }

        .mobile-menu {
            margin-left: auto;

            .menu-icon {
                background: transparent;
                border: none;
                font-size: 4rem;
            }
        }
    }

    div {
        &.listens {
            p {
                font-size: 2.2rem;
            }

            .powered-container {
                img {
                    padding-right: 45px
                }
            }
        }
    }

    section {
        &.r-banner {
            .banner__inner {
                padding: 50px 200px 50px 40px;
            }
        }

        &.r-contact {
            p {
                max-width: 80%;
            }
        }

        &.r-cta {
            .uk-flex {
                display: block;
            }

            .text {
                padding: 2em;
            }

            .image-container {
                .image {
                    width: 100%;
                }
            }
        }

        &.r-podcasts {
            .podcast {
                span.number {
                    font-size: 20rem;
                    line-height: 260px;
                }

                p {
                    font-size: 2rem;
                    line-height: 28px;
                }
            }

            img.wp-post-image {
                max-width: 200px;
            }
        }

        &.r-posts {
            .post {
                .bg {
                    min-height: 215px
                }
            }
        }

        &.r-tabs {
            .uk-subnav {
                display: block;

                li {
                    margin-bottom: 15px;
                    a {
                        padding: 20px;
                        font-size: 2.8rem;
                        line-height: 38px;
                    }
                }
            }

            .uk-switcher {
                li {
                    padding: 2%;
                }
            }
        }
    }

    footer.footer {
        text-align: center;

        &::before {
            top: 135px;
        }

        .foot-logo {
            top: 0;
            position: inherit;
            padding: 0;
            transform: none;
            margin: 20px 0;
        }

        a {
            display: block;
            margin: 0;

            &::after {
                display: none;
            }
        }
    }
}

@media (max-width: 782px) {

    section {
        &.r-single-podcast {
            margin-top: 0;

            .wp-block-columns {
                display: block;

                .wp-block-column:not(:first-child) {
                    margin: 0;
                }

                figure.alignright {
                    float: none;
                }

                &.icons {
                    .wp-block-column {
                        display: inline-block;
                        width: 25%;
                        margin: 0 !important;
                    }
                }
            }
        }
    }
}

@media (max-width: 667px) {

    .uk-container {
        padding-left: 30px;
        padding-right: 30px;
    }

    a {
        &.blue {
            font-size: 1.6rem;
        }
    }

    span.icon-podcast-blue {
        width: 28px;
        height: 19px;
        mask: none;
        background: url(../img/icon-podcast-blue.svg);
        background-size: 100%;
    }

    p {
        font-size: 1.8rem;
        line-height: 32px;
    }

    h1, h2, h3, h4, h5, h6 {
        margin-bottom: 20px;
    }

    h1 {
        font-size: 3.8rem;
        line-height: 45px;
    }

    h2 {
        font-size: 2.8rem;
        line-height: 35px;
    }

    h3 {
        font-size: 2rem;
        line-height: 25px;
    }

    h4 {
        font-size: 1.7rem;
        line-height: 20px;
    }

    .wp-block-buttons {
        .wp-block-button {
            &.is-style-square {
                .wp-block-button__link {
                    font-size: 2.4rem;
                }
            }
        }
    }

    div {
        &.listens {
            padding: 0;

            .uk-container {
                max-width: inherit;
                padding: 0 0 0 10px;
            }

            p {
                font-size: 1.45rem;
            }

            .powered-container {
                &::before {
                    border-top: 20px solid transparent;
                    border-bottom: 20px solid transparent;
                    border-left: 15px solid #313131;
                }

                img {
                    padding: 15px;
                    padding-left: 25px;
                    max-width: 160px;
                }
            }
        }

        &.single-post {
            .post-body {
                padding-top: 3em;
            }
        }
    }

    form {
        input:not([type=image]), select, textarea {
            padding: .5em !important;
        }
    }

    header.header {
        padding: 25px 0;

        .logo {
            img {
                max-width: 175px;
            }
        }

        .mobile-menu {
            .menu-icon {
                font-size: 3rem;
            }
        }
    }

    section {
        &.r-banner {
            .banner__inner {
                padding: 1em;
                min-height: 270px
            }
        }

        &.r-cta {
            .text {
                padding: 1.5em;

                p {
                    font-size: 2rem;
                    line-height: 34px;
                }
            }
        }

        &.r-podcasts {
            padding: 1em 0;

            .podcast {
                padding: 1em 0;
                text-align: center;
                border-bottom: 4px solid @dark;
                padding-bottom: 65px;
                margin-bottom: 50px;

                &:last-child {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    border: none;
                }

                &::before {
                    display: none;
                }

                span.number {
                    display: none;
                }

                a.blue {
                    display: block;
                }
            }
        }

        &.r-single-podcast {
            .wp-block-columns {
                &.icons {
                    .wp-block-column {
                        width: 48%;
                    }
                }
            }

            .wp-block-image {
                figure {
                    figcaption {
                        font-size: 1.8rem;
                    }
                }
            }
        }

        &.r-tabs {
            .uk-accordion {
                li {
                    .uk-accordion-title {
                        padding: 29px 65px 29px 25px;
                        font-size: 1.7rem;
                        line-height: 30px;
                    }

                    a {
                        &::after {
                            right: 20px;
                            top: 39px;
                            background-size: 75%;
                        }
                    }

                    &.uk-open {
                        .uk-accordion-title {
                            &::after {
                                width: 35px;
                                height: 35px;
                                background-size: 100%;
                            }
                        }
                    }
                }
            }
        }

        &.r-posts {
            .post {
                h3 {
                    font-size: 2rem;
                    line-height: 28px;
                }

                p {
                    font-size: 1.6rem;
                    line-height: 24px;
                }
            }
        }
    }

    footer.footer {
        padding-top: 2em;
    }
}