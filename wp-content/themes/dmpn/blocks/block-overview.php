<?php // Block Name: Overview ?>
<?php // Variables
    $ID = $block['id'];
    $bg = get_field('background_image');
?>

<section id="<?= $ID ?>" class="<?= $block['className'] ?> r-overview <?= ($bg) ? 'has-bg' : 'has-no-bg'; ?>" style="background: url(<?= $bg['url'] ?>) no-repeat center center / cover;">

    <div class="uk-container">

        <?php $i = 0; ?>

        <?php while(have_rows('rows')) : the_row(); ?>

            <?php 

                $title = get_sub_field('title');
                $text  = get_sub_field('text');
                $label = get_sub_field('label');
                $url   = get_sub_field('url');
                $image = get_sub_field('image');

            ?>

            <?php if($i % 2 == 0) : ?>

                <div class="row">

                    <div class="uk-child-width-1-2@m" uk-grid>

                        <div data-aos="fade-right">

                            <div class="text">

                                <h2 class="section-title"><?= $title ?></h2>

                                <?= $text ?>

                                <a href="<?= $url ?>" class="blue"><?= $label ?></a>

                            </div>

                        </div>

                        <div class="uk-text-right" data-aos="fade-left">

                            <img src="<?= $image['url'] ?>" alt="<?= $image['alt'] ?>">

                        </div>

                    </div>

                </div>

            <?php else : ?>

                <div class="row">

                    <div class="uk-child-width-1-2@m" uk-grid>

                        <div class="uk-text-left" data-aos="fade-right">

                            <img src="<?= $image['url'] ?>" alt="<?= $image['alt'] ?>">

                        </div>

                        <div data-aos="fade-left">

                            <div class="text">

                                <h2 class="section-title"><?= $title ?></h2>

                                <?= $text ?>

                                <a href="<?= $url ?>" class="blue"><?= $label ?></a>

                            </div>

                        </div>

                    </div>

                </div>

            <?php endif; ?>

            <?php $i++; ?>

        <?php endwhile; ?>

    </div>

</section>