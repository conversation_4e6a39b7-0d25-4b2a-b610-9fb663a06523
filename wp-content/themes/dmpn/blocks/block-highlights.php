<?php // Block Name: Highlights ?>
<?php // Variables
    $ID     = $block['id'];
    $title  = get_field('title');
    $anchor = get_field('anchor');
    $quote  = get_field('quote');
    $author = get_field('author');
    $image  = get_field('image');
?>

<section id="<?= $ID ?>" class="r-highlights">

    <div class="uk-container">

        <h2 class="section-title" data-aos="fade-up"><?= $title ?></h2>

        <?php if(have_rows('highlights')) : ?>

            <div class="uk-child-width-1-2@m uk-child-width-1-1 uk-grid-match uk-grid-large" uk-grid>

                <?php while(have_rows('highlights')) : the_row(); ?>

                    <?php 

                        $text = get_sub_field('text');

                        $percent = get_sub_field('percent');

                        $compare = get_sub_field('compare');

                        $icon = get_sub_field('icon');

                        $type = get_sub_field('type');

                        $text = get_sub_field('text');

                    ?>

                    <div data-aos="fade-up">

                        <?php if($type == 'icon') : ?>

                        <div class="highlight">

                            <p><?= $text ?></p>

                            <div class="uk-flex uk-flex-middle">

                                <?php if($icon != 'plus') : ?>

                                    <img width="100" height="100" src="<?= get_template_directory_uri() ?>/img/<?= $icon ?>.svg" alt="<?= $icon ?>">

                                <?php endif; ?>

                                <h2 class="text-large"><?= $percent ?></h2>

                                <?php if($icon == 'plus') : ?>

                                    <img class="is-plus" width="50" height="50" src="<?= get_template_directory_uri() ?>/img/<?= $icon ?>.svg" alt="<?= $icon ?>">

                                <?php endif; ?>

                            </div>

                            <?php if($compare) : ?>

                                <i><?= $compare ?></i>

                            <?php endif; ?>

                        </div>

                        <?php endif; ?>

                        <?php if($type == 'text') : ?>

                        <div class="quote">

                            <blockquote>

                                <?= $text ?>

                                <p class="author">- <?= $author ?></p>

                            </blockquote>

                        </div>

                        <?php endif; ?>

                    </div>

                <?php endwhile; ?>

            </div>

        <?php endif; ?>

        <?php if($quote) : ?>

            <blockquote data-aos="fade-up">

                <?= $quote ?>

                <p class="author">- <?= $author ?></p>

            </blockquote>

        <?php endif; ?>

        <?php if($image) : ?>

            <div class="uk-text-center uk-margin-large-top">
                
                <img data-aos="fade-up" src="<?= $image['url'] ?>" alt="<?= $image['alt'] ?>">

            </div>

        <?php endif; ?>

        <?php if($anchor) : ?>

            <a data-aos="fade-up" href="<?= $anchor ?>">Read the details below <i class="fas fa-angle-double-down"></i></a>

        <?php endif; ?>

    </div>

</section>