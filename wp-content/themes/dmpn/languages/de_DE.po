msgid ""
msgstr ""
"Project-Id-Version: HTML5 Blank WordPress Theme\n"
"POT-Creation-Date: 2013-09-18 00:25+0100\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON><PERSON> | www.egado.de <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> | www.egado.de <<EMAIL>>\n"
"Language: German\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.5.5\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: _e;__\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPath-1: ..\n"

#: ../404.php:9
msgid "Page not found"
msgstr "<PERSON>hler 404 – Die Seite wurde leider nicht gefunden."

#: ../404.php:11
msgid "Return home?"
msgstr "Zurück zur Startseite?"

#: ../archive.php:6
msgid "Archives"
msgstr "Archive"

#: ../author.php:8
msgid "Author Archives for "
msgstr "Autor-Archiv für "

#: ../author.php:14
msgid "About "
msgstr "Über "

#: ../author.php:41 ../loop.php:22 ../single.php:27
msgid "Published by"
msgstr "Veröffentlicht von"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "Leave your thoughts"
msgstr "Schreibe einen Kommentar"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "1 Comment"
msgstr "1 Kommentar"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "% Comments"
msgstr "% Kommentare"

#: ../author.php:61 ../loop.php:39 ../page.php:31 ../single.php:53
#: ../template-demo.php:31
msgid "Sorry, nothing to display."
msgstr "Nichts gefunden, was den Suchkriterien entspricht."

#: ../category.php:6
msgid "Categories for"
msgstr "Kategorie-Archiv für"

#: ../comments.php:3
msgid "Post is password protected. Enter the password to view any comments."
msgstr ""
"Dieser Artikel ist passwortgeschützt. Um die Kommentare sehen zu können muss "
"das Passwort eingegeben werden."

#: ../comments.php:18
msgid "Comments are closed here."
msgstr "Kommentare sind geschlossen."

#: ../footer.php:6
msgid "Powered by"
msgstr "Betrieben mit"

#: ../functions.php:133
msgid "Header Menu"
msgstr "Kopfbereichs-Menü"

#: ../functions.php:134
msgid "Sidebar Menu"
msgstr "Seitenleisten-Menü"

#: ../functions.php:135
msgid "Extra Menu"
msgstr "Zusatz-Menü"

#: ../functions.php:181
msgid "Widget Area 1"
msgstr "Widget-Bereich 1"

#: ../functions.php:182 ../functions.php:193
msgid "Description for this widget-area..."
msgstr "Beschreibung für diesen Widget-Bereich…"

#: ../functions.php:192
msgid "Widget Area 2"
msgstr "Widget-Bereich 2"

#: ../functions.php:258
msgid "View Article"
msgstr "Artikel ansehen"

#: ../functions.php:319
#, php-format
msgid "<cite class=\"fn\">%s</cite> <span class=\"says\">says:</span>"
msgstr "<cite class=\"fn\">%s</cite> <span class=\"says\">sagt:</span>"

#: ../functions.php:322
msgid "Your comment is awaiting moderation."
msgstr "Dein Kommentar muss noch moderiert werden – Bitte hab etwas Geduld."

#: ../functions.php:328
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s um %2$s"

#: ../functions.php:328
msgid "(Edit)"
msgstr "(Bearbeiten)"

#: ../functions.php:411 ../functions.php:412
msgid "HTML5 Blank Custom Post"
msgstr "HTML5 Blank Custom Post-Type"

#: ../functions.php:413
msgid "Add New"
msgstr "Neuen Artikel erstellen"

#: ../functions.php:414
msgid "Add New HTML5 Blank Custom Post"
msgstr "Neuen HTML5 Blank Custom Post erstellen"

#: ../functions.php:415
msgid "Edit"
msgstr "Bearbeiten"

#: ../functions.php:416
msgid "Edit HTML5 Blank Custom Post"
msgstr "HTML5 Blank Custom Post bearbeiten"

#: ../functions.php:417
msgid "New HTML5 Blank Custom Post"
msgstr "Neuer HTML5 Blank Custom Post"

#: ../functions.php:418 ../functions.php:419
msgid "View HTML5 Blank Custom Post"
msgstr "HTML5 Blank Custom Post ansehen"

#: ../functions.php:420
msgid "Search HTML5 Blank Custom Post"
msgstr "HTML5 Blank Custom Post suchen"

#: ../functions.php:421
msgid "No HTML5 Blank Custom Posts found"
msgstr "Keine HTML5 Blank Custom Posts gefunden"

#: ../functions.php:422
msgid "No HTML5 Blank Custom Posts found in Trash"
msgstr "Keine HTML5 Blank Custom Posts im Papierkorb gefunden"

#: ../index.php:6
msgid "Latest Posts"
msgstr "Letzte Artikel"

#: ../search.php:6
#, php-format
msgid "%s Search Results for "
msgstr "%s Suchergebnisse für "

#: ../searchform.php:3
msgid "To search, type and hit enter."
msgstr "Suchbegriff…"

#: ../searchform.php:4
msgid "Search"
msgstr "Suchen"

#: ../single.php:33
msgid "Tags: "
msgstr "Stichwörter: "

#: ../single.php:35
msgid "Categorised in: "
msgstr "Kategorisiert in: "

#: ../single.php:37
msgid "This post was written by "
msgstr "Dieser Artikel wurde verfasst von "

#: ../tag.php:6
msgid "Tag Archive: "
msgstr "Stichword-Archiv: "

#~ msgid "%s results for "
#~ msgstr "%s Ergebnisse für "
