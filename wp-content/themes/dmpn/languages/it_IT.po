msgid ""
msgstr ""
"Project-Id-Version: HTML5 Blank WordPress Theme\n"
"POT-Creation-Date: 2013-08-31 17:10+0100\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: Italiano\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.5.7\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: _e;__\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPath-1: ..\n"

#: ../404.php:9
msgid "Page not found"
msgstr "Pagina non trovata"

#: ../404.php:11
msgid "Return home?"
msgstr "Tornare all'homepage?"

#: ../archive.php:6
msgid "Archives"
msgstr "Archivi"

#: ../author.php:8
msgid "Author Archives for "
msgstr "Archivio dell'autore "

#: ../author.php:14
msgid "About"
msgstr "Informazioni"

#: ../author.php:41 ../loop.php:22 ../single.php:27
msgid "Published by"
msgstr "Pubblicato da"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "Leave your thoughts"
msgstr "Lascia il tuo commento"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "1 Comment"
msgstr "1 Commento"

#: ../author.php:42 ../loop.php:23 ../single.php:28
msgid "% Comments"
msgstr "% Commenti"

#: ../author.php:61 ../loop.php:39 ../page.php:31 ../single.php:53
#: ../template-demo.php:31
msgid "Sorry, nothing to display."
msgstr "Spiacenti, contenuto non disponibile."

#: ../category.php:6
msgid "Categories for"
msgstr "Categoria per"

#: ../comments.php:3
msgid "Post is password protected. Enter the password to view any comments."
msgstr ""
"Questo post è protetto da password. Inserisci la password per vedere i "
"commenti."

#: ../comments.php:18
msgid "Comments are closed here."
msgstr "Non è più possibile commentare."

#: ../footer.php:6
msgid "Powered by"
msgstr "Prodotto da"

#: ../functions.php:133
msgid "Header Menu"
msgstr "Menu di pagina"

#: ../functions.php:134
msgid "Sidebar Menu"
msgstr "Menu della spalla"

#: ../functions.php:135
msgid "Extra Menu"
msgstr "Menu extra"

#: ../functions.php:181
msgid "Widget Area 1"
msgstr "Area widget 1"

#: ../functions.php:182 ../functions.php:193
msgid "Description for this widget-area..."
msgstr "Descrizione per questa area widget..."

#: ../functions.php:192
msgid "Widget Area 2"
msgstr "Area widget 2"

#: ../functions.php:258
msgid "View Article"
msgstr "Mostra articolo"

#: ../functions.php:319
#, php-format
msgid "<cite class=\"fn\">%s</cite> <span class=\"says\">says:</span>"
msgstr "<span class=\"fn\">%s</span> <span class=\"says\">ha detto:</span>"

#: ../functions.php:322
msgid "Your comment is awaiting moderation."
msgstr "Il tuo commento è in attesa di moderazione."

#: ../functions.php:328
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s a %2$s"

#: ../functions.php:328
msgid "(Edit)"
msgstr "(Modifica)"

#: ../functions.php:411 ../functions.php:412
msgid "HTML5 Blank Custom Post"
msgstr "Articolo personalizzato"

#: ../functions.php:413
msgid "Add New"
msgstr "Aggiungi nuovo"

#: ../functions.php:414
msgid "Add New HTML5 Blank Custom Post"
msgstr "Aggiungi un nuovo articolo personalizzato"

#: ../functions.php:415
msgid "Edit"
msgstr "Modifica"

#: ../functions.php:416
msgid "Edit HTML5 Blank Custom Post"
msgstr "Modifica articolo personalizzato"

#: ../functions.php:417
msgid "New HTML5 Blank Custom Post"
msgstr "Nuovo articolo personalizzato"

#: ../functions.php:418 ../functions.php:419
msgid "View HTML5 Blank Custom Post"
msgstr "Mostra articolo personalizzato"

#: ../functions.php:420
msgid "Search HTML5 Blank Custom Post"
msgstr "Cerca articolo personalizzato"

#: ../functions.php:421
msgid "No HTML5 Blank Custom Posts found"
msgstr "Nessun articolo personalizzato trovato"

#: ../functions.php:422
msgid "No HTML5 Blank Custom Posts found in Trash"
msgstr "Nessun articolo personalizzato trovato nel cestino"

#: ../index.php:6
msgid "Latest Posts"
msgstr "Ultimi articoli"

#: ../search.php:6
#, php-format
msgid "%s Search Results for "
msgstr "%s Risultati di ricerca per "

#: ../searchform.php:4
msgid "Search"
msgstr "Cerca"

#: ../single.php:33
msgid "Tags: "
msgstr "Tag: "

#: ../single.php:35
msgid "Categorised in: "
msgstr "Categoria: "

#: ../single.php:37
msgid "This post was written by "
msgstr "Questo articolo è stato scritto da "

#: ../tag.php:6
msgid "Tag Archive: "
msgstr "Archivio tag: "

#~ msgid "%s results for "
#~ msgstr "%s risultati per "
