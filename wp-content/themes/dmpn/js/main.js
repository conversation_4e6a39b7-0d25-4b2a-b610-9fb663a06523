jQuery(function ($) {

	AOS.init({
      offset: 200,
      duration: 800,
      easing: 'ease-out-quart',
      disable: 'mobile',
      once: true
    });

	$('.uk-offcanvas-bar li.menu-item-has-children').click(function(){
		$(this).toggleClass('active');
	});

	$('.r-tabs .uk-subnav > li, .r-tabs .uk-switcher > li').each(function(){
        $(this).removeClass('uk-active');
    });


	$(".listen-container p > span").counterUp({
		delay: 20,
	    time: 1000
	});
});