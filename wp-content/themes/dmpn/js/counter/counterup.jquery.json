{"name": "counterup", "version": "1.0.0", "title": "Counter-Up", "description": "A lightweight jQuery plugin that counts up to a targeted number when the number becomes visible.", "keywords": ["j<PERSON>y", "plugin", "counter", "count", "up", "number", "figure", "numeric", "int", "float", "animation"], "homepage": "https://github.com/bfintal/Counter-Up", "author": {"name": "<PERSON>", "url": "https://github.com/bfintal/"}, "bugs": "https://github.com/bfintal/Counter-Up/issues", "licenses": [{"type": "GPL2", "url": "https://raw.github.com/bfintal/Counter-Up/master/LICENSE"}], "dependencies": {"jquery": ">=1.5", "waypoints": ">=2.0"}, "demo": "http://bfintal.github.io/Counter-Up/demo/demo.html"}