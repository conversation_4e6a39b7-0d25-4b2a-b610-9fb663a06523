wp.domReady( () => {
	wp.blocks.unregisterBlockStyle( 'core/button', 'default' );
	wp.blocks.unregisterBlockStyle( 'core/button', 'outline' );
    wp.blocks.unregisterBlockStyle( 'core/button', 'squared' );
    wp.blocks.unregisterBlockStyle( 'core/button', 'fill' );

    // New registered block styles for core blocks
    wp.blocks.registerBlockStyle( 'core/button', {
        name: 'square',
        label: 'Squre Button'
    } );

    wp.blocks.registerBlockStyle( 'core/buttons', {
        name: 'flex',
        label: 'Flex Buttons'
    } );

} );

