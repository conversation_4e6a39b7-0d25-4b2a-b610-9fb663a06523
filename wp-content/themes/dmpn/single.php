<?php get_header(); ?>

<main class="has-pattern">
	<?php while(have_posts()) : the_post(); ?>
		<div class="single-post">

			<?php if(has_post_thumbnail()) : ?>
				<div class="uk-text-center">
					<?= get_the_post_thumbnail() ?>
				</div>
			<?php endif; ?>

			<section class="intro">
				 <div class="uk-container">
					<div class="uk-width-1-2@s">
						<h1><?= get_the_title(); ?></h1>
						<div class="author uk-flex uk-flex-middle">
							<div class="avatar-container">
								<?php echo get_avatar( get_the_author_meta( 'ID' ), 32 ); ?>
							</div>
							<p>By <?= get_the_author(); ?> on <?= get_the_date(); ?></p>
						</div>
					</div>
				 </div>
			</section>

			<section class="post-body">
				<div class="uk-container">
					<div class="uk-width-2-3@s">
						<div class="content">
							<?php the_content(); ?>
						</div>
					</div>
				</div>
			</section>

			<section class="post-share">
				<div class="uk-container">
					<div class="share uk-flex uk-flex-middle">
						<p>Share</p>
						<div id="share">
							<a href="https://www.facebook.com/sharer/sharer.php?u=<?php the_permalink(); ?>" target="_blank" rel="noopener noreferrer"><span class="facebook"></span></a>
							<a href="https://pinterest.com/pin/create/button/?description=&media=&url=<?php the_permalink(); ?>" target="_blank"><span class="pinterest"></span></a>
							<a href="https://twitter.com/home?status=<?php the_permalink(); ?>" target="_blank" rel="noopener noreferrer"><span class="twitter"></span></a>
							<a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php the_permalink(); ?>&title=&summary=&source=" target="_blank" rel="noopener noreferrer"><span class="linkedin"></span></a>
						</div>
					</div>
				</div>
			</section>
		</div>
	<?php endwhile; ?>
</main>

<?php get_footer(); ?>